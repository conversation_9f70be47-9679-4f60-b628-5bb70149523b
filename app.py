import random
import gradio as gr
import numpy as np
import spaces
import torch
import os
from huggingface_hub import hf_hub_download
from diffusers import DiffusionPipeline
from compel import Compel, ReturnedEmbeddingsType
from PIL import Image, PngImagePlugin
import json
import io

# Add metadata to the image
def add_metadata_to_image(image, metadata):
    metadata_str = json.dumps(metadata)
    
    # Convert PIL Image to PNG with metadata
    img_with_metadata = image.copy()
    
    # Create a PngInfo object and add metadata
    png_info = PngImagePlugin.PngInfo()
    png_info.add_text("parameters", metadata_str)
    
    # Save to a byte buffer with metadata
    buffer = io.BytesIO()
    img_with_metadata.save(buffer, format="PNG", pnginfo=png_info)
    
    # Reopen from buffer to get the image with metadata
    buffer.seek(0)
    return Image.open(buffer)

def add_comma_after_pattern_ti(text):
    pattern = re.compile(r'\b\w+_\d+\b')
    modified_text = pattern.sub(lambda x: x.group() + ',', text)
    return modified_text
    
if not torch.cuda.is_available():
    DESCRIPTION += "\n<p>你现在运行在CPU上 但是此项目只支持GPU.</p>"

MAX_SEED = np.iinfo(np.int32).max
MAX_IMAGE_SIZE = 2048

if torch.cuda.is_available():
    dtype = torch.bfloat16
    pipe = DiffusionPipeline.from_pretrained("Qwen/Qwen-Image", torch_dtype=dtype).to("cuda")

def randomize_seed_fn(seed: int, randomize_seed: bool) -> int:
    if randomize_seed:
        seed = random.randint(0, MAX_SEED)
    return seed

@spaces.GPU
def infer(
    prompt: str,
    negative_prompt: str = "lowres, {bad}, error, fewer, extra, missing, worst quality, jpeg artifacts, bad quality, watermark, unfinished, displeasing, chromatic aberration, signature, extra digits, artistic error, username, scan, [abstract]",
    use_negative_prompt: bool = True,
    seed: int = 7,
    width: int = 1024,
    height: int = 1536,
    guidance_scale: float = 3,
    num_inference_steps: int = 30,
    randomize_seed: bool = True,
    use_resolution_binning: bool = True,
    progress=gr.Progress(track_tqdm=True),
):
    seed = int(randomize_seed_fn(seed, randomize_seed))
    generator = torch.Generator().manual_seed(seed)
    # 初始化 Compel 实例
    compel = Compel(
        tokenizer=[pipe.tokenizer, pipe.tokenizer_2],
        text_encoder=[pipe.text_encoder, pipe.text_encoder_2],
        returned_embeddings_type=ReturnedEmbeddingsType.PENULTIMATE_HIDDEN_STATES_NON_NORMALIZED,
        requires_pooled=[False, True],
        truncate_long_prompts=False
    )
    # 在 infer 函数中调用 get_embed_new
    if not use_negative_prompt:
        negative_prompt = ""
    
    original_prompt = prompt  # Store original prompt for metadata
    prompt = get_embed_new(prompt, pipe, compel, only_convert_string=True)
    negative_prompt = get_embed_new(negative_prompt, pipe, compel, only_convert_string=True)
    conditioning, pooled = compel([prompt, negative_prompt]) # 必须同时处理来保证长度相等
    
    # 在调用 pipe 时，使用新的参数名称（确保参数名称正确）
    image = pipe(
        prompt_embeds=conditioning[0:1],
        pooled_prompt_embeds=pooled[0:1],
        negative_prompt_embeds=conditioning[1:2],
        negative_pooled_prompt_embeds=pooled[1:2],
        width=width,
        height=height,
        guidance_scale=guidance_scale,
        num_inference_steps=num_inference_steps,
        generator=generator,
        use_resolution_binning=use_resolution_binning,
    ).images[0]

    # Create metadata dictionary
    metadata = {
        "prompt": original_prompt,
        "processed_prompt": prompt,
        "negative_prompt": negative_prompt,
        "seed": seed,
        "width": width,
        "height": height,
        "guidance_scale": guidance_scale,
        "num_inference_steps": num_inference_steps,
        "model": "miaomiaoHarem_vPredDogma11",
        "use_resolution_binning": use_resolution_binning,
        "PreUrl": "https://huggingface.co/spaces/Menyu/miaomiaoHaremDogma11"
    }
    # Add metadata to the image
    image_with_metadata = add_metadata_to_image(image, metadata)
    
    return image_with_metadata, seed

examples = [
    "nahida (genshin impact)",
    "klee (genshin impact)",
]

css = '''
.gradio-container {
    max-width: 560px !important;
    margin-left: auto !important;
    margin-right: auto !important;
}
h1{text-align:center}
'''

with gr.Blocks(css=css) as demo:
    gr.Markdown("""# 梦羽的模型生成器
        ### 快速生成 MiaomiaoHarem vPred Dogma 1.1 模型的图片""")
    with gr.Group():
        with gr.Row():
            prompt = gr.Text(
                label="关键词",
                show_label=True,
                max_lines=5,
                placeholder="输入你要的图片关键词",
                container=False,
            )
            run_button = gr.Button("生成", scale=0, variant="primary")
        result = gr.Image(label="Result", show_label=False, format="png")
    with gr.Accordion("高级选项", open=False):
        with gr.Row():
            use_negative_prompt = gr.Checkbox(label="使用反向词条", value=True)
            negative_prompt = gr.Text(
                label="反向词条",
                max_lines=5,
                lines=4,
                placeholder="输入你要排除的图片关键词",
                value="lowres, {bad}, error, fewer, extra, missing, worst quality, jpeg artifacts, bad quality, watermark, unfinished, displeasing, chromatic aberration, signature, extra digits, artistic error, username, scan, [abstract]",
                visible=True,
            )
        seed = gr.Slider(
            label="种子",
            minimum=0,
            maximum=MAX_SEED,
            step=1,
            value=0,
        )
        randomize_seed = gr.Checkbox(label="随机种子", value=True)
        with gr.Row(visible=True):
            width = gr.Slider(
                label="宽度",
                minimum=512,
                maximum=MAX_IMAGE_SIZE,
                step=64,
                value=832,
            )
            height = gr.Slider(
                label="高度",
                minimum=512,
                maximum=MAX_IMAGE_SIZE,
                step=64,
                value=1216,
            )
        with gr.Row():
            guidance_scale = gr.Slider(
                label="Guidance Scale",
                minimum=0.1,
                maximum=10,
                step=0.1,
                value=7.0,
            )
            num_inference_steps = gr.Slider(
                label="生成步数",
                minimum=1,
                maximum=50,
                step=1,
                value=28,
            )

    gr.Examples(
        examples=examples,
        inputs=prompt,
        outputs=[result, seed],
        fn=infer
    )

    use_negative_prompt.change(
        fn=lambda x: gr.update(visible=x),
        inputs=use_negative_prompt,
        outputs=negative_prompt,
    )

    gr.on(
        triggers=[prompt.submit, run_button.click],
        fn=infer,
        inputs=[
            prompt,
            negative_prompt,
            use_negative_prompt,
            seed,
            width,
            height,
            guidance_scale,
            num_inference_steps,
            randomize_seed,
        ],
        outputs=[result, seed],
    )

if __name__ == "__main__":
    demo.launch(share=True)